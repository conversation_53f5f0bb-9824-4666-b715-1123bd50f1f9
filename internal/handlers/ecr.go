package handlers

import (
	"encoding/json"
	"io"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/usecase/ecr"
	"api-server/internal/usecase/kompose"
	"api-server/pkg/oteltrace"
	"api-server/pkg/validator"
)

// ECRHandler defines the interface for handling ECR-related requests
type ECRHandler interface {
	// // ListRepositories lists all ECR repositories
	// ListRepositories(c *gin.Context)
	//
	// // GetRepository gets information about a single ECR repository
	// GetRepository(c *gin.Context)
	//
	// // CreateRepository creates a new ECR repository
	// CreateRepository(c *gin.Context)
	//
	// // DeleteRepository deletes an ECR repository
	// DeleteRepository(c *gin.Context)
	//
	// // ListImages lists all images in a repository
	// ListImages(c *gin.Context)
	//
	// // DeleteImage deletes an image from an ECR repository
	// DeleteImage(c *gin.Context)
	//
	// // GetAuthorizationToken gets an authorization token for ECR
	// GetAuthorizationToken(c *gin.Context)

	CreateECRDeployment(c *gin.Context)

	// Update an existing ECR deployment
	UpdateECRDeployment(c *gin.Context)

	// Environment variable management
	CreateECRDeploymentEnv(c *gin.Context)
	UpdateECRDeploymentEnv(c *gin.Context)
	DeleteECRDeploymentEnv(c *gin.Context)

	// ListECRDeployment lists all ECR deployments with pagination
	ListECRDeployment(c *gin.Context)

	// GetECRDeployment gets an ECR deployment by ID
	GetECRDeployment(c *gin.Context)

	// DeployECR deploys an ECR image to Kubernetes
	DeployECR(c *gin.Context)

	// DeleteECRDeployment deletes an ECR deployment
	DeleteECRDeployment(c *gin.Context)

	// StopECRDeployment stops an ECR deployment
	StopECRDeployment(c *gin.Context)

	GetDeploymentPodLogs(c *gin.Context)

	GetDeploymentStatus(c *gin.Context)

	// ListComposeDeployment lists all Compose deployments with pagination
	ListComposeDeployment(c *gin.Context)
}

type ecrHandlerImpl struct {
	ecrUsecase     ecr.ECRUsecase
	komposeUsecase kompose.KomposeUsecase
	config         *configs.GlobalConfig
}

// NewECRHandler creates a new instance of ECRHandler.
// It initializes the handler with the global configuration and ECR use case.
//
// Parameters:
//   - config: The global configuration.
//   - ecrUsecase: The ECR use case.
//   - komposeUsecase: The Kompose use case.
//
// Returns:
//   - ECRHandler: A new ECRHandler instance.
func NewECRHandler(config *configs.GlobalConfig, ecrUsecase ecr.ECRUsecase, komposeUsecase kompose.KomposeUsecase) ECRHandler {
	return &ecrHandlerImpl{
		ecrUsecase:     ecrUsecase,
		komposeUsecase: komposeUsecase,
		config:         config,
	}
}

// // ListRepositories godoc
// //
// //	@Summary	List all ECR repositories
// //	@Tags		ECR
// //	@Produce	json
// //	@Success	200	{object}	dto.ECRRepositoriesResponse	"Return list of ECR repositories"
// //	@Failure	401	{object}	dto.HTTPError				"Unauthorized - Invalid or expired token"
// //	@Failure	403	{object}	dto.HTTPError				"Forbidden - User does not have required permissions"
// //	@Failure	500	{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
// //	@Router		/ecr/repositories [get]
// //
// //	@Security	Bearer
// func (h *ecrHandlerImpl) ListRepositories(c *gin.Context) {
// 	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.ListRepositories")
// 	defer span.End()
//
// 	// Check user permissions
// 	currentUserID, err := GetCurrentUserId(ctx)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get current user id")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewForbiddenError(err, http.StatusText(http.StatusForbidden)))
// 		return
// 	}
// 	span.AddEvent("Got current user ID", trace.WithAttributes(attribute.String("user.id", fmt.Sprintf("%d", currentUserID))))
//
// 	// List ECR repositories
// 	repositories, err := h.ecrUsecase.ListRepositories(ctx)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to list ECR repositories")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
// 		return
// 	}
//
// 	// Return response
// 	c.JSON(http.StatusOK, dto.ECRRepositoriesResponse{
// 		Data: &repositories,
// 	})
// }
//
// // GetRepository godoc
// //
// //	@Summary	Get information about a single ECR repository
// //	@Tags		ECR
// //	@Produce	json
// //	@Param		repositoryName	path		string						true	"Repository name"
// //	@Success	200				{object}	dto.ECRRepositoryResponse	"Return repository information"
// //	@Failure	401				{object}	dto.HTTPError				"Unauthorized - Invalid or expired token"
// //	@Failure	403				{object}	dto.HTTPError				"Forbidden - User does not have required permissions"
// //	@Failure	404				{object}	dto.HTTPError				"Not Found - Repository not found"
// //	@Failure	500				{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
// //	@Router		/ecr/repositories/{id} [get]
// //
// //	@Security	Bearer
// func (h *ecrHandlerImpl) GetRepository(c *gin.Context) {
// 	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.GetRepository")
// 	defer span.End()
//
// 	// Check user permissions
// 	currentUserID, err := GetCurrentUserId(ctx)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get current user id")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewForbiddenError(err, http.StatusText(http.StatusForbidden)))
// 		return
// 	}
// 	span.AddEvent("Got current user ID", trace.WithAttributes(attribute.String("user.id", fmt.Sprintf("%d", currentUserID))))
//
// 	// bind query parameters
// 	var req dto.GetECRRepositoryRequest
// 	if err := c.ShouldBindQuery(&req); err != nil {
// 		span.SetStatus(codes.Error, "failed to bind query parameters")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewBadRequestError(err))
// 		return
// 	}
//
// 	// Get repository name from URL path
// 	id := c.Param("ecr_id")
// 	if id == "" {
// 		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "ID is required"))
// 		return
// 	}
// 	resourceUUID, err := uuid.Parse(id)
// 	if err != nil {
// 		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "ID is invalid"))
// 		return
// 	}
//
// 	// Get repository name using UUID
// 	repositoryName := h.ecrUsecase.GetRepositoryName(ctx, req.Type, resourceUUID)
// 	// Get repository information
// 	repository, err := h.ecrUsecase.GetRepository(ctx, repositoryName)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get ECR repository")
// 		span.RecordError(err)
// 		if err.Error() == "repository not found: "+repositoryName {
// 			dto.ErrorResponse(c, dto.NewNotFoundError(err, "Repository not found"))
// 			return
// 		}
// 		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
// 		return
// 	}
//
// 	// Return response
// 	c.JSON(http.StatusOK, dto.ECRRepositoryResponse{
// 		Data: repository,
// 	})
// }
//
// // CreateRepository godoc
// //
// //	@Summary	Create a new ECR repository
// //	@Tags		ECR
// //	@Accept		json
// //	@Produce	json
// //	@Param		repository	body		dto.ECRRepositoryCreateInput	true	"Repository creation input"
// //	@Success	201			{object}	dto.ECRRepositoryResponse		"Return created repository information"
// //	@Failure	400			{object}	dto.HTTPError					"Bad Request - Invalid input"
// //	@Failure	401			{object}	dto.HTTPError					"Unauthorized - Invalid or expired token"
// //	@Failure	403			{object}	dto.HTTPError					"Forbidden - User does not have required permissions"
// //	@Failure	500			{object}	dto.HTTPError					"Internal Server Error - Encountered an unexpected condition"
// //	@Router		/ecr/repositories [post]
// //
// //	@Security	Bearer
// func (h *ecrHandlerImpl) CreateRepository(c *gin.Context) {
// 	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.CreateRepository")
// 	defer span.End()
//
// 	// Check user permissions
// 	currentUserID, err := GetCurrentUserId(ctx)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get current user id")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewForbiddenError(err, http.StatusText(http.StatusForbidden)))
// 		return
// 	}
// 	span.AddEvent("Got current user ID", trace.WithAttributes(attribute.String("user.id", currentUserID.String())))
//
// 	// Parse and validate request body
// 	var input dto.ECRRepositoryCreateInput
// 	if err := c.ShouldBindJSON(&input); err != nil {
// 		span.SetStatus(codes.Error, "failed to bind JSON input")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewBadRequestError(err))
// 		return
// 	}
//
// 	// Create repository
// 	repository, err := h.ecrUsecase.CreateRepository(ctx, currentUserID, "user", input)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to create ECR repository")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
// 		return
// 	}
//
// 	// Return response
// 	c.JSON(http.StatusCreated, dto.ECRRepositoryResponse{
// 		Data: repository,
// 	})
// }
//
// // DeleteRepository godoc
// //
// //	@Summary	Delete an ECR repository
// //	@Tags		ECR
// //	@Produce	json
// //	@Param		repositoryName	path	string	true	"Repository name"
// //	@Success	204				"No Content - Repository deleted successfully"
// //	@Failure	401				{object}	dto.HTTPError	"Unauthorized - Invalid or expired token"
// //	@Failure	403				{object}	dto.HTTPError	"Forbidden - User does not have required permissions"
// //	@Failure	404				{object}	dto.HTTPError	"Not Found - Repository not found"
// //	@Failure	500				{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
// //	@Router		/ecr/repositories/{id} [delete]
// //
// //	@Security	Bearer
// func (h *ecrHandlerImpl) DeleteRepository(c *gin.Context) {
// 	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.DeleteRepository")
// 	defer span.End()
//
// 	// Check user permissions
// 	currentUserID, err := GetCurrentUserId(ctx)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get current user id")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewForbiddenError(err, http.StatusText(http.StatusForbidden)))
// 		return
// 	}
// 	span.AddEvent("Got current user ID", trace.WithAttributes(attribute.String("user.id", fmt.Sprintf("%d", currentUserID))))
//
// 	// bind query parameters
// 	var req dto.DeleteECRRepositoryRequest
// 	if err := c.ShouldBindQuery(&req); err != nil {
// 		span.SetStatus(codes.Error, "failed to bind query parameters")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewBadRequestError(err))
// 		return
// 	}
//
// 	// Get repository name from URL path
// 	id := c.Param("ecr_id")
// 	if id == "" {
// 		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "ID is required"))
// 		return
// 	}
// 	resourceUUID, err := uuid.Parse(id)
// 	if err != nil {
// 		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "ID is invalid"))
// 		return
// 	}
//
// 	// Get repository name using UUID
// 	repositoryName := h.ecrUsecase.GetRepositoryName(ctx, req.Type, resourceUUID)
// 	// Delete repository
// 	err = h.ecrUsecase.DeleteRepository(ctx, repositoryName)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to delete ECR repository")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
// 		return
// 	}
//
// 	// Return success with no content
// 	c.Status(http.StatusNoContent)
// }
//
// // ListImages godoc
// //
// //	@Summary	List all images in an ECR repository
// //	@Tags		ECR
// //	@Produce	json
// //	@Param		repositoryName	path		string					true	"Repository name"
// //	@Success	200				{object}	dto.ECRImagesResponse	"Return list of ECR images"
// //	@Failure	401				{object}	dto.HTTPError			"Unauthorized - Invalid or expired token"
// //	@Failure	403				{object}	dto.HTTPError			"Forbidden - User does not have required permissions"
// //	@Failure	404				{object}	dto.HTTPError			"Not Found - Repository not found"
// //	@Failure	500				{object}	dto.HTTPError			"Internal Server Error - Encountered an unexpected condition"
// //	@Router		/ecr/repositories/{id}/images [get]
// //
// //	@Security	Bearer
// func (h *ecrHandlerImpl) ListImages(c *gin.Context) {
// 	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.ListImages")
// 	defer span.End()
//
// 	// Check user permissions
// 	currentUserID, err := GetCurrentUserId(ctx)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get current user id")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewForbiddenError(err, http.StatusText(http.StatusForbidden)))
// 		return
// 	}
// 	span.AddEvent("Got current user ID", trace.WithAttributes(attribute.String("user.id", fmt.Sprintf("%d", currentUserID))))
//
// 	// bind query parameters
// 	var req dto.ListECRImagesRequest
// 	if err := c.ShouldBindQuery(&req); err != nil {
// 		span.SetStatus(codes.Error, "failed to bind query parameters")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewBadRequestError(err))
// 		return
// 	}
//
// 	// Get repository name from URL path
// 	id := c.Param("ecr_id")
// 	if id == "" {
// 		span.SetStatus(codes.Error, "failed to get id from URL path")
// 		span.RecordError(errors.New("ID in path is empty"))
// 		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "ID is required"))
// 		return
// 	}
// 	resourceUUID, err := uuid.Parse(id)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to parse UUID")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "ID is invalid"))
// 		return
// 	}
//
// 	// Get repository name using UUID
// 	repositoryName := h.ecrUsecase.GetRepositoryName(ctx, req.Type, resourceUUID)
// 	// List images
// 	images, nextToken, err := h.ecrUsecase.ListImages(ctx, ecr.ListImagesInput{
// 		RepositoryName: repositoryName,
// 		PerPage:        req.PerPage,
// 		NextToken:      req.NextPageToken,
// 	})
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to list ECR images")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
// 		return
// 	}
//
// 	// set X-Next-Page-Token header if there is a next page to get more results
// 	if nextToken != nil {
// 		c.Header("X-Next-Page-Token", *nextToken)
// 	}
//
// 	c.Header("Access-Control-Expose-Headers", "*")
//
// 	// Return response
// 	c.JSON(http.StatusOK, dto.ECRImagesResponse{
// 		Data: &images,
// 	})
// }
//
// // GetAuthorizationToken godoc
// //
// //	@Summary	Get an authorization token for ECR
// //	@Tags		ECR
// //	@Produce	json
// //	@Success	200	{object}	dto.ECRAuthorizationTokenResponse	"Return ECR authorization token"
// //	@Failure	401	{object}	dto.HTTPError						"Unauthorized - Invalid or expired token"
// //	@Failure	403	{object}	dto.HTTPError						"Forbidden - User does not have required permissions"
// //	@Failure	500	{object}	dto.HTTPError						"Internal Server Error - Encountered an unexpected condition"
// //	@Router		/ecr/:id/creds [get]
// //
// //	@Security	Bearer
// func (h *ecrHandlerImpl) GetAuthorizationToken(c *gin.Context) {
// 	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.GetAuthorizationToken")
// 	defer span.End()
//
// 	// Check user permissions
// 	currentUserID, err := GetCurrentUserId(ctx)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get current user id")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewForbiddenError(err, http.StatusText(http.StatusForbidden)))
// 		return
// 	}
// 	span.AddEvent("Got current user ID", trace.WithAttributes(attribute.String("user.id", fmt.Sprintf("%d", currentUserID))))
//
// 	// Get authorization token
// 	resp, err := h.ecrUsecase.GetAuthorizationToken(ctx, currentUserID)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get ECR authorization token")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
// 		return
// 	}
//
// 	c.JSON(http.StatusOK, dto.ECRAuthorizationTokenResponse{
// 		Data: &dto.ECRAuthorizationToken{
// 			Password:           resp.Password,
// 			AuthorizationToken: resp.AuthorizationToken,
// 			ExpiresAt:          resp.ExpiresAt,
// 			ProxyEndpoint:      resp.ProxyEndpoint,
// 		},
// 	})
//
// }
//
// // DeleteImage godoc
// //
// //	@Summary	Delete an image from an ECR repository
// //	@Tags		ECR
// //	@Accept		json
// //	@Produce	json
// //	@Param		id	path	string						true	"Repository ID"
// //	@Param		req	query	dto.DeleteECRImageRequest	true	"Image identifier (digest or tags)"
// //	@Success	204	"No Content - Image deleted successfully"
// //	@Failure	400	{object}	dto.HTTPError	"Bad Request - Invalid input"
// //	@Failure	401	{object}	dto.HTTPError	"Unauthorized - Invalid or expired token"
// //	@Failure	403	{object}	dto.HTTPError	"Forbidden - User does not have required permissions"
// //	@Failure	404	{object}	dto.HTTPError	"Not Found - Repository or image not found"
// //	@Failure	500	{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
// //	@Router		/ecr/repositories/{id}/images [delete]
// //
// //	@Security	Bearer
// func (h *ecrHandlerImpl) DeleteImage(c *gin.Context) {
// 	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.DeleteImage")
// 	defer span.End()
//
// 	// Check user permissions
// 	currentUserID, err := GetCurrentUserId(ctx)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to get current user id")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewForbiddenError(err, http.StatusText(http.StatusForbidden)))
// 		return
// 	}
// 	span.AddEvent("Got current user ID", trace.WithAttributes(attribute.String("user.id", fmt.Sprintf("%d", currentUserID))))
//
// 	// Get repository ID from URL path
// 	id := c.Param("ecr_id")
// 	if id == "" {
// 		span.SetStatus(codes.Error, "failed to get id from URL path")
// 		span.RecordError(errors.New("ID in path is empty"))
// 		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "ID is required"))
// 		return
// 	}
//
// 	resourceUUID, err := uuid.Parse(id)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to parse UUID")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "ID is invalid"))
// 		return
// 	}
//
// 	// Bind query parameters
// 	var req dto.DeleteECRImageRequest
// 	if err := c.ShouldBindQuery(&req); err != nil {
// 		span.SetStatus(codes.Error, "failed to bind query parameters")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewBadRequestError(err))
// 		return
// 	}
//
// 	// Validate that at least one identifier is provided
// 	if req.ImageDigest == "" && len(req.ImageTags) == 0 {
// 		err := errors.New("either image digest or at least one image tag must be provided")
// 		span.SetStatus(codes.Error, err.Error())
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewBadRequestError(err))
// 		return
// 	}
//
// 	// Get repository name using UUID
// 	repositoryName := h.ecrUsecase.GetRepositoryName(ctx, req.Type, resourceUUID)
// 	// Delete the image
// 	err = h.ecrUsecase.DeleteImage(ctx, repositoryName, req)
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to delete ECR image")
// 		span.RecordError(err)
// 		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
// 		return
// 	}
//
// 	// Return success with no content
// 	c.Status(http.StatusNoContent)
// }

// CreateECRDeployment godoc
//
//	@Summary	Create a new ECR deployment
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		input	body		dto.CreateECRDeploymentInput	true	"Deployment configuration"
//	@Success	201		{object}	dto.CreateECRDeploymentResponse	"Return deployment information"
//	@Failure	400		{object}	dto.HTTPError					"Bad Request - Invalid input"
//	@Failure	401		{object}	dto.HTTPError					"Unauthorized - Invalid or expired token"
//	@Failure	403		{object}	dto.HTTPError					"Forbidden - User does not have required permissions"
//	@Failure	500		{object}	dto.HTTPError					"Internal Server Error"
//	@Router		/ecr/deployments [post]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) CreateECRDeployment(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.CreateECRDeployment")
	defer span.End()

	// Get current user ID
	currentUserID, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	var input dto.CreateECRDeploymentInput

	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	// Parse and validate request body
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	// Create deployment
	deploymentInput := ecr.CreateECRDeploymentInput{
		Name:      input.DeploymentName,
		ImageURI:  input.ImageURI,
		NodeName:  input.NodeName,
		Port:      input.Port,
		Env:       input.Env,
		OwnerID:   currentUserID,
		OwnerType: input.OwnerType,
	}

	result, err := h.ecrUsecase.CreateECRDeployment(ctx, currentUserID, deploymentInput)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create ECR deployment")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	// Return response
	c.JSON(http.StatusOK, dto.CreateECRDeploymentResponse{
		Data: result,
	})
}

// CreateECRDeploymentEnv godoc
//
//	@Summary	Create a new environment variable for an ECR deployment
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		id		path	string							true	"Deployment ID"
//	@Param		input	body	dto.CreateECRDeploymentEnvInput	true	"Environment variable input"
//	@Success	204		"No Content - Environment variable created successfully"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - Invalid input"
//	@Failure	401		{object}	dto.HTTPError	"Unauthorized - Invalid or expired token"
//	@Failure	403		{object}	dto.HTTPError	"Forbidden - User does not have required permissions"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error"
//	@Router		/ecr/deployments/{id}/env [post]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) CreateECRDeploymentEnv(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.CreateECRDeploymentEnv")
	defer span.End()

	// Get current user ID
	currentUserID, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	// Get deployment ID from path
	id := c.Param("ecr_id")
	if id == "" {
		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "deployment ID is required"))
		return
	}

	deploymentID, err := uuid.Parse(id)
	if err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid deployment ID"))
		return
	}

	// Bind request body
	var input dto.CreateECRDeploymentEnvInput
	if err := c.ShouldBindJSON(&input); err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}
	input.ID = deploymentID

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	// Create environment variable
	err = h.ecrUsecase.CreateECRDeploymentEnv(ctx, currentUserID, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create environment variable")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// UpdateECRDeploymentEnv godoc
//
//	@Summary	Update an environment variable for an ECR deployment
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		id		path	string							true	"Deployment ID"
//	@Param		input	body	dto.UpdateECRDeploymentEnvInput	true	"Environment variable input"
//	@Success	204		"No Content - Environment variable updated successfully"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - Invalid input"
//	@Failure	401		{object}	dto.HTTPError	"Unauthorized - Invalid or expired token"
//	@Failure	403		{object}	dto.HTTPError	"Forbidden - User does not have required permissions"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error"
//	@Router		/ecr/deployments/{id}/env [put]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) UpdateECRDeploymentEnv(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.UpdateECRDeploymentEnv")
	defer span.End()

	// Get current user ID
	currentUserID, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	// Get deployment ID from path
	id := c.Param("ecr_id")
	if id == "" {
		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "deployment ID is required"))
		return
	}

	deploymentID, err := uuid.Parse(id)
	if err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid deployment ID"))
		return
	}

	// Bind request body
	var input dto.UpdateECRDeploymentEnvInput
	if err := c.ShouldBindJSON(&input); err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}
	input.ID = deploymentID

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	// Update environment variable
	err = h.ecrUsecase.UpdateECRDeploymentEnv(ctx, currentUserID, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to update environment variable")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// DeleteECRDeploymentEnv godoc
//
//	@Summary	Delete an environment variable from an ECR deployment
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		id		path	string							true	"Deployment ID"
//	@Param		input	body	dto.DeleteECRDeploymentEnvInput	true	"Environment variable input"
//	@Success	204		"No Content - Environment variable deleted successfully"
//	@Failure	400		{object}	dto.HTTPError	"Bad Request - Invalid input"
//	@Failure	401		{object}	dto.HTTPError	"Unauthorized - Invalid or expired token"
//	@Failure	403		{object}	dto.HTTPError	"Forbidden - User does not have required permissions"
//	@Failure	500		{object}	dto.HTTPError	"Internal Server Error"
//	@Router		/ecr/deployments/{id}/env [delete]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) DeleteECRDeploymentEnv(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.DeleteECRDeploymentEnv")
	defer span.End()

	// Get current user ID
	currentUserID, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	// Get deployment ID from path
	id := c.Param("ecr_id")
	if id == "" {
		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "deployment ID is required"))
		return
	}

	deploymentID, err := uuid.Parse(id)
	if err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid deployment ID"))
		return
	}

	// Bind request query
	var input dto.DeleteECRDeploymentEnvInput
	if err := c.ShouldBindQuery(&input); err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}
	input.ID = deploymentID

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	// Delete environment variable
	err = h.ecrUsecase.DeleteECRDeploymentEnv(ctx, currentUserID, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete environment variable")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// UpdateECRDeployment godoc
//
//	@Summary	Update an existing ECR deployment
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		id		path		string							true	"Deployment ID"
//	@Param		input	body		dto.UpdateECRDeploymentInput	true	"Deployment update configuration"
//	@Success	204		{object}	string							"Return updated deployment information"
//	@Failure	400		{object}	dto.HTTPError					"Bad Request - Invalid input"
//	@Failure	401		{object}	dto.HTTPError					"Unauthorized - Invalid or expired token"
//	@Failure	403		{object}	dto.HTTPError					"Forbidden - User does not have required permissions"
//	@Failure	404		{object}	dto.HTTPError					"Not Found - Deployment not found"
//	@Failure	500		{object}	dto.HTTPError					"Internal Server Error"
//	@Router		/ecr/deployments/{id} [put]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) UpdateECRDeployment(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.UpdateECRDeployment")
	defer span.End()

	// Get current user ID
	currentUserID, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	// Get deployment ID from path
	id := c.Param("ecr_id")
	if id == "" {
		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "deployment ID is required"))
		return
	}

	deploymentID, err := uuid.Parse(id)
	if err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid deployment ID"))
		return
	}

	// Parse and validate request body
	var input dto.UpdateECRDeploymentInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	// Call usecase method
	updateInput := ecr.UpdateECRDeploymentInput{
		DeploymentID: deploymentID,
		Port:         input.Port,
		NodeName:     input.NodeName,
		NumCpu:       input.NumCpu,
		Mem:          input.Mem,
	}

	// Convert ProxyBodySize from *uint to *int if provided
	if input.IngressConfig != nil {
		updateInput.ProxyBodySize = input.IngressConfig.ProxyBodySize
	}

	result, err := h.ecrUsecase.UpdateECRDeployment(ctx, currentUserID, updateInput)
	if err != nil {
		span.SetStatus(codes.Error, "failed to update ECR deployment")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}
	_ = result

	// Return response
	c.JSON(http.StatusNoContent, nil)
}

// ListECRDeployment godoc
//
//	@Summary	List all ECR deployments
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		page		query		int		false	"Page number"		default(1)
//	@Param		per_page	query		int		false	"Items per page"	default(10)
//	@Param		order_by	query		string	false	"Order by field"	default(created_at)
//	@Param		direction	query		string	false	"Order direction"	Enums(asc, desc)
//	@Param		user_id		query		string	false	"Filter by user ID"
//	@Param		org_id		query		string	false	"Filter by org ID"
//	@Success	200			{object}	dto.ListECRDeploymentResponse
//	@Failure	400			{object}	dto.HTTPError	"Bad Request"
//	@Failure	401			{object}	dto.HTTPError	"Unauthorized"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error"
//	@Router		/ecr/deployments [get]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) ListECRDeployment(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.ListECRDeployment")
	defer span.End()

	// Get current user ID
	currentUserID, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	// Parse and validate query parameters
	var query dto.ListECRDeploymentInput
	if err := c.ShouldBindQuery(&query); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}
	if err := validator.Validate(query); err != nil {
		span.SetStatus(codes.Error, "failed to validate query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	// Set default values for pagination
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PerPage <= 0 {
		query.PerPage = 10
	}

	// Call usecase
	deployments, total, err := h.ecrUsecase.ListECRDeployment(ctx, currentUserID, query)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list deployments")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	c.JSON(http.StatusOK, dto.ListECRDeploymentResponse{
		Data: &deployments,
		Pagination: &dto.Pagination{
			Total:    int(total),
			PageNo:   query.Page,
			PageSize: query.PerPage,
		},
	})
}

// GetECRDeployment godoc
//
//	@Summary	Get ECR deployment by ID
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		id	path		string	true	"Deployment ID"
//	@Success	200	{object}	dto.GetECRDeploymentResponse
//	@Failure	400	{object}	dto.HTTPError	"Bad Request"
//	@Failure	401	{object}	dto.HTTPError	"Unauthorized"
//	@Failure	403	{object}	dto.HTTPError	"Forbidden"
//	@Failure	404	{object}	dto.HTTPError	"Not Found"
//	@Failure	500	{object}	dto.HTTPError	"Internal Server Error"
//	@Router		/ecr/deployments/{id} [get]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) GetECRDeployment(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.GetECRDeployment")
	defer span.End()

	// Get current user ID
	currentUserID, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	// Get deployment ID from path
	id := c.Param("ecr_id")
	if id == "" {
		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "deployment ID is required"))
		return
	}

	deploymentID, err := uuid.Parse(id)
	if err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid deployment ID"))
		return
	}

	// Get deployment
	deployment, err := h.ecrUsecase.GetECRDeployment(ctx, currentUserID, deploymentID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get ECR deployment")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	c.JSON(http.StatusOK, dto.GetECRDeploymentResponse{
		Data: deployment,
	})
}

// DeployECR godoc
//
//	@Summary	Deploy an ECR image to Kubernetes
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		id	path		string			true	"Deployment ID"
//	@Success	204	{object}	string			"No Content"
//	@Failure	400	{object}	dto.HTTPError	"Bad Request - Invalid input"
//	@Failure	401	{object}	dto.HTTPError	"Unauthorized - Invalid or expired token"
//	@Failure	403	{object}	dto.HTTPError	"Forbidden - User does not have required permissions"
//	@Failure	404	{object}	dto.HTTPError	"Not Found - Deployment not found"
//	@Failure	500	{object}	dto.HTTPError	"Internal Server Error"
//	@Router		/ecr/deployments/{id}/deploy [post]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) DeployECR(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.DeployECR")
	defer span.End()

	// Get current user ID
	currentUserID, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	// Get deployment ID from path
	id := c.Param("ecr_id")
	if id == "" {
		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "deployment ID is required"))
		return
	}

	deploymentID, err := uuid.Parse(id)
	if err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid deployment ID"))
		return
	}

	// Call usecase to deploy
	result, err := h.ecrUsecase.DeployECR(ctx, currentUserID, deploymentID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to deploy ECR")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}
	_ = result

	// Return response
	c.JSON(http.StatusNoContent, nil)
}

// DeleteECRDeployment godoc
//
//	@Summary	Delete an ECR deployment
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		id	path	string	true	"Deployment ID"
//	@Success	204	"No Content - Deployment deleted successfully"
//	@Failure	400	{object}	dto.HTTPError	"Bad Request - Invalid input"
//	@Failure	401	{object}	dto.HTTPError	"Unauthorized - Invalid or expired token"
//	@Failure	403	{object}	dto.HTTPError	"Forbidden - User does not have required permissions"
//	@Failure	404	{object}	dto.HTTPError	"Not Found - Deployment not found"
//	@Failure	500	{object}	dto.HTTPError	"Internal Server Error"
//	@Router		/ecr/deployments/{id} [delete]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) DeleteECRDeployment(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.DeleteECRDeployment")
	defer span.End()

	// Get current user ID
	currentUserID, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	// Get deployment ID from path
	id := c.Param("ecr_id")
	if id == "" {
		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "deployment ID is required"))
		return
	}

	deploymentID, err := uuid.Parse(id)
	if err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid deployment ID"))
		return
	}

	// Delete deployment
	err = h.ecrUsecase.DeleteECRDeployment(ctx, currentUserID, deploymentID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete ECR deployment")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// StopECRDeployment godoc
//
//	@Summary	Stop an ECR deployment
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		id	path	string	true	"Deployment ID"
//	@Success	204	"No Content - Deployment stopped successfully"
//	@Failure	400	{object}	dto.HTTPError	"Bad Request - Invalid input"
//	@Failure	401	{object}	dto.HTTPError	"Unauthorized - Invalid or expired token"
//	@Failure	403	{object}	dto.HTTPError	"Forbidden - User does not have required permissions"
//	@Failure	404	{object}	dto.HTTPError	"Not Found - Deployment not found"
//	@Failure	500	{object}	dto.HTTPError	"Internal Server Error"
//	@Router		/ecr/deployments/{id}/stop [post]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) StopECRDeployment(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.StopECRDeployment")
	defer span.End()

	// Get deployment ID from path
	id := c.Param("ecr_id")
	if id == "" {
		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "deployment ID is required"))
		return
	}

	deploymentID, err := uuid.Parse(id)
	if err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid deployment ID"))
		return
	}

	// Stop deployment
	err = h.ecrUsecase.StopECRDeployment(ctx, deploymentID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to stop ECR deployment")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// GetDeploymentPodLogs godoc
//
//	@Summary	Retrieve deployment pod logs by deployment ID
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		id	path		string							true	"Deployment ID"
//	@Success	200	{object}	dto.GetDeploymentLogsResponse	"Return deployment pod logs"
//	@Failure	400	{object}	dto.HTTPError					"Bad Request - invalid request"
//	@Failure	404	{object}	dto.HTTPError					"Not Found"
//	@Failure	500	{object}	dto.HTTPError					"Internal Server Error - Encountered an unexpected condition"
//	@Router		/ecr/deployments/{id}/pods/logs [get]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) GetDeploymentPodLogs(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.ecr.GetECRDeploymentPodLogs")
	defer span.End()

	// Get deployment ID from path
	id := c.Param("ecr_id")
	if id == "" {
		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "deployment ID is required"))
		return
	}

	deploymentID, err := uuid.Parse(id)
	if err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid deployment ID"))
		return
	}

	logChan, err := h.ecrUsecase.GetPodLogs(ctx, deploymentID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get pod logs")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	c.Writer.Header().Set("X-Accel-Buffering", "no")

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()
	c.Stream(func(w io.Writer) bool {
		select {
		case <-ticker.C:
			c.SSEvent("heartbeat", "heartbeat")
		case msg := <-logChan:
			jsonBytes, err := json.Marshal(msg)
			if err != nil {
				span.SetStatus(codes.Error, "failed to encode log message")
				span.RecordError(err)
				c.SSEvent("terminate", "error encoding JSON")
				return false
			}
			c.SSEvent("message", string(jsonBytes))
		case <-ctx.Done():
			if ctx.Err() != nil {
				span.SetStatus(codes.Error, "context cancelled")
				span.RecordError(ctx.Err())
			}
			c.SSEvent("terminate", ctx.Err().Error())
			return false
		}
		return true
	})
}

func (h *ecrHandlerImpl) GetDeploymentStatus(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.ecr.GetDeploymentStatus")
	defer span.End()

	// Get deployment ID from path
	id := c.Param("ecr_id")
	if id == "" {
		dto.ErrorResponse(c, dto.NewBadRequestError(nil, "deployment ID is required"))
		return
	}

	deploymentID, err := uuid.Parse(id)
	if err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "invalid deployment ID"))
		return
	}

	resp, err := h.ecrUsecase.GetDeploymentStatus(ctx, deploymentID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get deployment status")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("successfully fetched deployment status")
	span.SetStatus(codes.Ok, "successfully fetched deployment status")
	c.JSON(http.StatusOK, resp)
}

// ListComposeDeployment godoc
//
//	@Summary	List all Compose deployments
//	@Tags		ECR
//	@Accept		json
//	@Produce	json
//	@Param		page		query		int		false	"Page number"		default(1)
//	@Param		per_page	query		int		false	"Items per page"	default(10)
//	@Param		order_by	query		string	false	"Order by field"	default(created_at)
//	@Param		direction	query		string	false	"Order direction"	Enums(asc, desc)
//	@Param		user_id		query		string	false	"Filter by user ID"
//	@Param		org_id		query		string	false	"Filter by org ID"
//	@Success	200			{object}	dto.ListComposeDeploymentInput
//	@Failure	400			{object}	dto.HTTPError	"Bad Request"
//	@Failure	401			{object}	dto.HTTPError	"Unauthorized"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error"
//	@Router		/composes [get]
//
//	@Security	Bearer
func (h *ecrHandlerImpl) ListComposeDeployment(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.ecr.ListComposeDeployment")
	defer span.End()

	// Parse and validate query parameters
	var query dto.ListComposeDeploymentInput
	if err := c.ShouldBindQuery(&query); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}
	if err := validator.Validate(query); err != nil {
		span.SetStatus(codes.Error, "failed to validate query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	// Set default values for pagination
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PerPage <= 0 {
		query.PerPage = 10
	}

	// Call usecase
	deployments, total, err := h.komposeUsecase.ListComposeDeployment(ctx, query)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list deployments")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	c.JSON(http.StatusOK, dto.ListComposeDeploymentResponse{
		Data: &deployments,
		Pagination: &dto.Pagination{
			Total:    int(total),
			PageNo:   query.Page,
			PageSize: query.PerPage,
		},
	})
}
